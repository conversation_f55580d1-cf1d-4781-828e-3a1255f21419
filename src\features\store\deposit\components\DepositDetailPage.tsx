import React, { useState, useMemo } from 'react';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useDepositDetail } from '@/features/store/deposit/hooks/useDepositDetail';
import { formatDateJapan, formatNumber, getPreviousMonthRangeByTransferDate } from '@/utils/dateUtils';
import { mapTransactionType } from '@/constants/common.constant';
import { PDFTemplates } from '@/services/pdfTemplates';
import { CSVLink } from 'react-csv';
import { CSVExportDetailData } from '../types/depositDetail';
import { useDynamicTableWidth } from '@/hooks/useDynamicTableWidth';
import { ArrowDownNarrowWide, ArrowDownWideNarrow, Download } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import _ from 'lodash';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface DepositDetailPageProps {
  merchantNo: string;
  paymentBId: string;
  transactionType: string;
  transferDate: string;
};

type SortField = 'agxTransactionDate' | 'agxTransactionType' | 'agxSalesAmount';
type SortDirection = 'asc' | 'desc';

export const DepositDetailPage: React.FC<DepositDetailPageProps> = ({ merchantNo, paymentBId, transactionType, transferDate }) => {
  const { user } = useAuthStore();
  const agxStoreName = user?.agxStoreName || '';
  const tableMaxWidth = useDynamicTableWidth();

  // Sorting state
  const [sortField, setSortField] = useState<SortField | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  // Thêm state cho filter
  const [searchUsageDate, setSearchUsageDate] = useState<string>('all');
  const [searchTransactionType, setSearchTransactionType] = useState<string>('all');
  const [tempSearchUsageDate, setTempSearchUsageDate] = useState<string>('all');
  const [tempSearchTransactionType, setTempSearchTransactionType] = useState<string>('all');

  // Thêm state cho export hover
  const [isExportHovered, setIsExportHovered] = useState(false);

  const {
    depositDetail,
    totalSalesAmount,
    isLoading,
    error
  } = useDepositDetail(
    merchantNo,
    paymentBId,
    transactionType || '',
    transferDate || ''
  );

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Lọc và sort data theo filter
  const filteredSortedData = useMemo(() => {
    if (!depositDetail.data) return [];
    let filtered = [...depositDetail.data];
    if (searchUsageDate !== 'all') {
      filtered = filtered.filter(item => item.agxTransactionDate === searchUsageDate);
    }
    if (searchTransactionType !== 'all') {
      const selectedTypeNumber = parseInt(searchTransactionType);
      filtered = filtered.filter(item => item.agxTransactionType === selectedTypeNumber);
    }
    if (!sortField) return filtered;
    return [...filtered].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;
      switch (sortField) {
        case 'agxTransactionDate':
          aValue = a.agxTransactionDate;
          bValue = b.agxTransactionDate;
          break;
        case 'agxTransactionType':
          aValue = `${mapTransactionType.get(a.agxTransactionType)}${a.groupCodeName}`;
          bValue = `${mapTransactionType.get(b.agxTransactionType)}${b.groupCodeName}`;
          break;
        case 'agxSalesAmount':
          aValue = a.agxSalesAmount;
          bValue = b.agxSalesAmount;
          break;
        default:
          return 0;
      }
      if (sortField === 'agxSalesAmount') {
        const numA = Number(aValue);
        const numB = Number(bValue);
        return sortDirection === 'asc' ? numA - numB : numB - numA;
      } else {
        const strA = String(aValue);
        const strB = String(bValue);
        const comparison = strA.localeCompare(strB);
        return sortDirection === 'asc' ? comparison : -comparison;
      }
    });
  }, [depositDetail.data, sortField, sortDirection, searchUsageDate, searchTransactionType]);

  // Lấy danh sách ngày và loại giao dịch duy nhất
  const uniqueUsageDates = useMemo(() => {
    if (!depositDetail.data) return [];
    const unique = _.uniq(depositDetail.data.map(item => item.agxTransactionDate));
    return unique.sort((a, b) => b.localeCompare(a));
  }, [depositDetail.data]);
  const uniqueTransactionTypes = useMemo(() => {
    if (!depositDetail.data) return [];
    const unique = _.uniqBy(depositDetail.data, 'agxTransactionType');
    return unique.map(item => ({
      value: item.agxTransactionType,
      label: `${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName}`
    })).sort((a, b) => a.label.localeCompare(b.label));
  }, [depositDetail.data]);

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowDownNarrowWide className="w-5 h-5 text-[#1D9987]" />;
    }
    return sortDirection === 'asc'
      ? <ArrowDownNarrowWide className="w-5 h-5 text-[#1D9987]" />
      : <ArrowDownWideNarrow className="w-5 h-5 text-[#1D9987]" />;
  };

  // CSV Export data
  const csvHeaders = [
    { label: '', key: 'agxTransactionDate' },
    { label: '', key: 'agxTransactionType' },
    { label: '', key: 'agxSalesAmount' },
    { label: '', key: 'agxPaymentDate' },
    { label: '', key: 'agxMemberId' }
  ];

  const getDataExport = (): CSVExportDetailData[] => {
    const result: CSVExportDetailData[] = [
      {
        agxTransactionDate: `加盟店ID: ${merchantNo}`,
        agxTransactionType: '',
        agxSalesAmount: '',
        agxPaymentDate: '',
        agxMemberId: ''
      },
      {
        agxTransactionDate: `店舗名: ${agxStoreName}`,
        agxTransactionType: '',
        agxSalesAmount: '',
        agxPaymentDate: '',
        agxMemberId: ''
      },
      {
        agxTransactionDate: `件数: ${depositDetail.totalElement}`,
        agxTransactionType: '',
        agxSalesAmount: '',
        agxPaymentDate: '',
        agxMemberId: ''
      },
      {
        agxTransactionDate: `売上額の合計: ${totalSalesAmount}`,
        agxTransactionType: '',
        agxSalesAmount: '',
        agxPaymentDate: '',
        agxMemberId: ''
      },
      {
        agxTransactionDate: '',
        agxTransactionType: '',
        agxSalesAmount: '',
        agxPaymentDate: '',
        agxMemberId: ''
      },
      {
        agxTransactionDate: '利用日',
        agxTransactionType: '取引種別',
        agxSalesAmount: '売上額',
        agxPaymentDate: '振込日',
        agxMemberId: '会員番号'
      }
    ];

    filteredSortedData.forEach(item => {
      result.push({
        agxTransactionDate: item.agxTransactionDate,
        agxTransactionType: `${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName}`,
        agxSalesAmount: item.agxSalesAmount.toString(),
        agxPaymentDate: item.agxPaymentDate,
        agxMemberId: item.agxMemberId
      });
    });

    return result;
  };

  const csvExport = {
    data: getDataExport(),
    headers: csvHeaders,
    filename: `deposit-detail-${transferDate}.csv`,
    separator: ',', // Sử dụng separator tùy chỉnh
    enclosingCharacter: '' // Bỏ dấu ngoặc kép bao quanh
  };

  const handleExportPDF = async () => {
    if (!filteredSortedData || filteredSortedData.length === 0) {
      alert('エクスポートするデータがありません。');
      return;
    }

    try {
      await PDFTemplates.generateDepositDetailPDF({
        detailData: filteredSortedData,
        merchantNo: merchantNo,
        transactionType: transactionType || '',
        datetime: transferDate || '',
        storeName: agxStoreName
      });
    } catch (error) {
      console.error('PDF export failed:', error);
      alert('PDF export failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mx-6 my-4">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!depositDetail.data) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-gray-600">データが見つかりません。</h2>
      </div>
    );
  }

  return (
    <div className="py-4 mb-16 text-lg text-[#6F6F6E]">
      {/* Header Section */}
      <div className="flex items-center py-4 gap-4 lg:gap-12 border-b border-[#6F6F6E] flex-wrap md:px-2 lg:px-6 px-4">
        {/* Filters and Export Section */}
        <div className='flex items-center lg:gap-10 gap-4 flex-wrap'>
          <div className='flex items-center lg:gap-8 gap-4 flex-wrap'>
            {/* Usage Date Search Field */}
            <div className='flex items-center md:gap-4 gap-2'>
              <Label className="text-2xl text-[#6F6F6E]">利用日</Label>
              <Select value={tempSearchUsageDate} onValueChange={setTempSearchUsageDate}>
                <div className="relative w-[200px] md:w-[240px] w-full">
                  <SelectTrigger className="text-[#6F6F6E] text-xl border-gray-500 border shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                    <SelectValue />
                  </SelectTrigger>
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
                  </div>
                </div>
                <SelectContent>
                  <SelectItem value="all" className="text-[#6F6F6E] text-xl">全て</SelectItem>
                  {uniqueUsageDates.map((date) => (
                    <SelectItem key={date} value={date} className="text-[#6F6F6E] text-xl">{date}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Transaction Type Search Field */}
            <div className='flex items-center md:gap-4 gap-2'>
              <Label className="text-2xl text-[#6F6F6E]">決済種別</Label>
              <Select value={tempSearchTransactionType} onValueChange={setTempSearchTransactionType}>
                <div className="relative w-[200px] md:w-[240px] w-full">
                  <SelectTrigger className="text-[#6F6F6E] text-xl border-gray-500 border shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                    <SelectValue />
                  </SelectTrigger>
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
                  </div>
                </div>
                <SelectContent>
                  <SelectItem value="all" className="text-[#6F6F6E] text-xl">全て</SelectItem>
                  {uniqueTransactionTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value.toString()} className="text-[#6F6F6E] text-xl">{type.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          {/* Search Button */}
          <Button
            variant="outline"
            onClick={() => {
              setSearchUsageDate(tempSearchUsageDate);
              setSearchTransactionType(tempSearchTransactionType);
            }}
            className="text-xl px-6 bg-[#F7F7F7] hover:bg-[#1D9987]/80 hover:text-white hover:border-[#1D9987] border-black text-[#6F6F6E] shadow-md"
          >
            検索
          </Button>
        </div>
        {/* Export Buttons - Following ExportButtons.tsx design pattern */}
        <div className="relative">
          <div
            className="relative"
            onMouseEnter={() => setIsExportHovered(true)}
            onMouseLeave={() => setIsExportHovered(false)}
          >
            {/* Download Icon Button */}
            <button
              className="p-2 text-[#1D9987] hover:text-[#1D9987]/80 hover:opacity-80 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={!filteredSortedData || filteredSortedData.length === 0}
            >
              <Download className="h-8 w-8" />
            </button>

            {/* Dropdown Menu */}
            {isExportHovered && filteredSortedData.length > 0 && (
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-32 bg-white border border-gray-300 rounded shadow-md z-50">
                {/* @ts-expect-error : type mismatch due to version node */}
                <CSVLink
                  data={csvExport.data}
                  headers={csvExport.headers}
                  filename={csvExport.filename}
                  separator={csvExport.separator}
                  enclosingCharacter={csvExport.enclosingCharacter}
                  className="block w-full px-6 py-3 text-center text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 border-b border-gray-200 transition-colors duration-150 no-underline"
                >
                  CSV
                </CSVLink>
                <button
                  onClick={handleExportPDF}
                  className="w-full px-6 py-3 text-center text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150"
                >
                  PDF
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Usage Period Display */}
        <span className="text-2xl">
          利用期間｜{
            (() => {
              const dateToUse = tempSearchUsageDate === 'all' ? (transferDate || '') : tempSearchUsageDate;
              const { start, end } = getPreviousMonthRangeByTransferDate(dateToUse);
              if (!start || !end) return '20xx年mm月dd日〜20xx年mm月dd日';
              return `${formatDateJapan(start)}〜${formatDateJapan(end)}`;
            })()
          }
        </span>
      </div>
      {/* Summary Information */}
      <div className="px-4 grid grid-cols-1 md:grid-cols-4 gap-2 md:px-2 lg:px-6 my-10 w-full text-2xl">
        <div>
          <span>加盟店ID: </span>
          <span>{merchantNo}</span>
        </div>
        <div>
          <span>店舗名: </span>
          <span>{agxStoreName}</span>
        </div>
        <div>
          <span>件数: </span>
          <span>{formatNumber(filteredSortedData.length)}</span>
        </div>
        <div>
          <span>売上額の合計: </span>
          <span>{formatNumber(filteredSortedData.reduce((sum, item) => sum + item.agxSalesAmount, 0))}</span>
        </div>
      </div>

      {/* Data Table */}
      <div className="overflow-x-auto mx-auto md:px-2" style={{ maxWidth: tableMaxWidth }}>
        <Table className="min-w-[1200px]">
          <TableHeader>
            <TableRow className="border-none">
              <TableHead className="text-center bg-white text-xl">
                <div className="w-[80%] inline-flex items-center justify-center gap-2 border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">
                  利用日
                  <button
                    onClick={() => handleSort('agxTransactionDate')}
                    className="rounded transition-colors"
                  >
                    {renderSortIcon('agxTransactionDate')}
                  </button>
                </div>
              </TableHead>
              <TableHead className="text-center bg-white text-xl" colSpan={2}>
                <div className="w-[80%] inline-flex items-center justify-center gap-2 border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">
                  取引種別
                  <button
                    onClick={() => handleSort('agxTransactionType')}
                    className="rounded transition-colors"
                  >
                    {renderSortIcon('agxTransactionType')}
                  </button>
                </div>
              </TableHead>
              <TableHead className="text-center bg-white text-xl">
                <div className="w-[80%] inline-flex items-center justify-center gap-2 border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">
                  売上額
                  <button
                    onClick={() => handleSort('agxSalesAmount')}
                    className="rounded transition-colors"
                  >
                    {renderSortIcon('agxSalesAmount')}
                  </button>
                </div>
              </TableHead>
              <TableHead className="text-center bg-white text-xl hidden">
                <span className="w-[80%] inline-block border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">振込日</span>
              </TableHead>
              <TableHead className="text-center bg-white text-xl">
                <span className="w-[80%] inline-block border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">会員番号</span>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredSortedData.length > 0 ? (
              filteredSortedData.map((item, index) => (
                <TableRow key={index} className="border-0">
                  <TableCell className="text-center bg-white text-xl">
                    <span className='w-[80%] inline-block'>{item.agxTransactionDate}</span>
                  </TableCell>
                  <TableCell className="text-center bg-white text-xl" colSpan={2}>
                    <span className='w-[80%] inline-block text-left px-4'>{`${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName}`}</span>
                  </TableCell>
                  <TableCell className="text-center bg-white text-xl">
                    <span className='w-[80%] inline-block'>{formatNumber(item.agxSalesAmount)}円</span>
                  </TableCell>
                  <TableCell className="text-center bg-white text-xl hidden">
                    <span className='w-[80%] inline-block'>{item.agxPaymentDate}</span>
                  </TableCell>
                  <TableCell className="text-center bg-white text-xl">
                    <span className='w-[80%] inline-block'>{item.agxMemberId}</span>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="px-4 py-8 text-center text-[#6F6F6E] bg-white text-xl">
                  データがありません
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
