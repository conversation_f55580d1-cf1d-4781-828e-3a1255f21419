import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { useDepositPage } from '@/features/store/deposit/hooks/useDepositPage';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { formatNumber, formatDateJapan, getPreviousMonthRangeByTransferDate } from '@/utils/dateUtils';
import { ExportButtons } from './ExportButtons';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { TaxSummaryTable } from '@/features/shared/deposit/components/TaxSummaryTable';
import { PaymentBreakdownTable } from '@/features/shared/deposit/components/PaymentBreakdownTable';
import { StoreSummaryTable } from '@/features/shared/deposit/components/StoreSummaryTable';
import {
  isCredictCardTransaction,
  isElectronicMoneyTransaction,
  isQRCodeTransaction
} from '@/features/shared/deposit/utils/transactionUtils';
import { useDynamicTableWidth } from '@/hooks/useDynamicTableWidth';
import { Label } from '@/components/ui/label';
// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

// Chart configuration constants
const CHART_COLORS = {
  backgroundColor: [
    'rgba(255, 99, 132, 0.6)',
    'rgba(255, 206, 86, 0.6)',
    'rgba(54, 162, 235, 0.6)',
  ],
  borderColor: [
    'rgba(255, 99, 132, 0.6)',
    'rgba(255, 206, 86, 0.6)',
    'rgba(54, 162, 235, 0.6)',
  ]
};

const CHART_OPTIONS = {
  responsive: true,
  maintainAspectRatio: false,
  layout: {
    padding: {
      top: 20,
      left: 20,
      right: 20
    }
  },
  plugins: {
    legend: {
      display: true,
      position: 'top' as const,
      labels: {
        fontColor: '#000',
        fontSize: 10,
        boxWidth: 10,
      },
      fit: 10
    },
    tooltips: {
      enabled: true
    },
  },
  cutout: '50%', // This makes it a doughnut chart
};



interface DepositPageProps {
  agxMerchantNo: string;
  type: string;
}

export const DepositPage: React.FC<DepositPageProps> = ({ agxMerchantNo, type }) => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const agxStoreName = user?.agxStoreName || '';
  const tableMaxWidth = useDynamicTableWidth();

  const {
    dates,
    depositData,
    transferDate,
    datesLoading,
    depositLoading,
    error,
    dlEnable,
    setTransferDate
  } = useDepositPage(agxMerchantNo);

  const [typeChart, setTypeChart] = useState("/store/deposit/" + type);

  // 11/06の振り込み分から新しいフォーマット
  const switchLayoutDate = "2023-11-05";

  // Calculate chart data using useMemo for better performance
  const chartData = useMemo(() => {
    if (!depositData?.paymentBreakdowns || !depositData?.merchantPayments?.length) {
      return {
        labelsNumberOfSales: [],
        labelsSalesAmount: [],
        percentageNumberOfSales: [],
        percentageSalesAmount: []
      };
    }

    const totalNumberOfSales = depositData.merchantPayments[0].agxNumberOfSales;
    const totalSalesAmount = depositData.merchantPayments[0].agxSalesAmount;

    let creditCardSales = 0;
    let creditCardAmount = 0;
    let electronicMoneySales = 0;
    let electronicMoneyAmount = 0;
    let qrCodeSales = 0;
    let qrCodeAmount = 0;

    depositData.paymentBreakdowns.forEach(element => {
      const transactionType = element.agxTransactionType;

      if (isCredictCardTransaction(transactionType)) {
        creditCardSales += element.agxNumberOfSales;
        creditCardAmount += element.agxSalesAmount;
      } else if (isElectronicMoneyTransaction(transactionType)) {
        electronicMoneySales += element.agxNumberOfSales;
        electronicMoneyAmount += element.agxSalesAmount;
      } else if (isQRCodeTransaction(transactionType)) {
        qrCodeSales += element.agxNumberOfSales;
        qrCodeAmount += element.agxSalesAmount;
      }
    });

    const creditCardSalesPercent = ((creditCardSales / totalNumberOfSales) * 100).toFixed(1);
    const electronicMoneySalesPercent = ((electronicMoneySales / totalNumberOfSales) * 100).toFixed(1);
    const qrCodeSalesPercent = ((qrCodeSales / totalNumberOfSales) * 100).toFixed(1);

    const creditCardAmountPercent = ((creditCardAmount / totalSalesAmount) * 100).toFixed(1);
    const electronicMoneyAmountPercent = ((electronicMoneyAmount / totalSalesAmount) * 100).toFixed(1);
    const qrCodeAmountPercent = ((qrCodeAmount / totalSalesAmount) * 100).toFixed(1);

    return {
      labelsNumberOfSales: [
        `クレジットカード決済[${creditCardSalesPercent}%]`,
        `電子マネー決済[${electronicMoneySalesPercent}%]`,
        `QRコード決済[${qrCodeSalesPercent}%]`
      ],
      labelsSalesAmount: [
        `クレジットカード決済[${creditCardAmountPercent}%]`,
        `電子マネー決済[${electronicMoneyAmountPercent}%]`,
        `QRコード決済[${qrCodeAmountPercent}%]`
      ],
      percentageNumberOfSales: [creditCardSalesPercent, electronicMoneySalesPercent, qrCodeSalesPercent],
      percentageSalesAmount: [creditCardAmountPercent, electronicMoneyAmountPercent, qrCodeAmountPercent]
    };
  }, [depositData]);

  const handleChangeTypeChart = (value: string) => {
    setTypeChart(value);
    navigate(value);
  };

  const handleChangeDate = (value: string) => {
    setTransferDate(value);
  };

  // Computed values for better readability
  const hasDepositData = !!depositData;
  const hasPaymentData = hasDepositData && depositData.merchantPayments?.length > 0;
  const isNewFormat = transferDate > switchLayoutDate;
  const showMonthlyFee = hasPaymentData && depositData.merchantPayments[0].agxInvoiceFlg !== 283260002;
  const showInvoiceFlag = hasPaymentData && depositData.merchantPayments[0].agxInvoiceFlg === 283260000;

  // Helper function to create chart data for Doughnut chart
  const createChartData = (labels: string[], percentages: string[]) => ({
    labels: labels,
    datasets: [
      {
        data: percentages.map(p => parseFloat(p)),
        backgroundColor: CHART_COLORS.backgroundColor,
        borderColor: CHART_COLORS.borderColor,
        borderWidth: 1,
        radius: 150,
      },
    ],
  });
  const createPlugins = (total: number) => [{
    id: 'centerText',
    beforeDraw: function (chart: any) {
      const width = chart.width;
      const height = chart.height;
      const ctx = chart.ctx;
      ctx.restore();
      const fontSize = (height / 220).toFixed(3);
      ctx.font = fontSize + "em sans-serif";
      ctx.textBaseline = "top";
      const text = formatNumber(total);
      const textX = Math.round((width - ctx.measureText(text).width) / 2);
      const textY = (height + 35) / 2;
      ctx.fillText(text, textX, textY);
      ctx.save();
    }
  }];

  if (datesLoading || depositLoading) {
    return (
      <LoadingSpinner />
    )
  }

  if (!hasDepositData) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-gray-600">振込データがありません。</h2>
      </div>
    )
  }

  return (
    <div className="px-4 md:px-0 pt-6 pb-2">
      {/* Header Section */}
      <div className="flex items-center pb-4 gap-2 sm:gap-8 lg:gap-12 border-b border-[#6F6F6E] flex-wrap md:px-2 lg:px-6">
        {/* Page Type Filter */}
        <div className='w-full sm:w-auto'>
          <Select value={typeChart} onValueChange={handleChangeTypeChart}>
            <div className="relative w-[250px] md:w-[290px] w-full">
              <SelectTrigger className="text-[#6F6F6E] text-xl border-gray-500 border shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                <SelectValue />
              </SelectTrigger>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
              </div>
            </div>
            <SelectContent>
              <SelectItem className="text-[#6F6F6E] text-xl" value={`/store/deposit/${type}`}>振込一覧</SelectItem>
              <SelectItem className='text-[#6F6F6E] text-xl' value={`/store/summary/${type}`}>売上金額・件数推移（振込日別）</SelectItem>
              <SelectItem className='text-[#6F6F6E] text-xl' value={`/store/summary-monthly/${type}`}>売上金額・件数推移（振込月別）</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Transfer Date Filter */}
        <div className="flex items-center gap-2 md:gap-8">
          {dates.length > 0 && (
            <>
              <Label className="text-2xl text-[#6F6F6E]">振込日</Label>
              <Select value={transferDate} onValueChange={handleChangeDate}>
                <div className="relative w-[200px] md:w-[240px] w-full">
                  <SelectTrigger className="text-[#6F6F6E] text-xl border-gray-500 border shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                    <SelectValue />
                  </SelectTrigger>
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
                  </div>
                </div>
                <SelectContent>
                  {dates.map((date, index) => (
                    <SelectItem className="text-[#6F6F6E] text-xl" key={index} value={date}>
                      {formatDateJapan(date)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </>
          )}
        </div>

        {depositData && (
          <ExportButtons
            depositData={depositData}
            transferDate={transferDate}
            agxMerchantNo={agxMerchantNo}
            agxStoreName={agxStoreName}
            type={type}
            dlEnable={dlEnable}
          />
        )}
        <span className="text-2xl ">
          利用期間｜{
            (() => {
              const { start, end } = getPreviousMonthRangeByTransferDate(transferDate);
              if (!start || !end) return '20xx年mm月dd日〜20xx年mm月dd日';
              return `${formatDateJapan(start)}〜${formatDateJapan(end)}`;
            })()
          }
        </span>
      </div>

      {error && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{error.message || '振込データがありません。'}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-6 text-[#6F6F6E]">
        {depositData && (
          <>
            {/* Store Info and Summary Table */}
            <div className='overflow-x-auto' style={{ maxWidth: tableMaxWidth }}>
              <StoreSummaryTable
                merchantPayments={depositData.merchantPayments || []}
                total={depositData.total}
                transferDate={transferDate}
                type={type}
                isNewFormat={isNewFormat}
                showMonthlyFee={showMonthlyFee}
              />
            </div>

            {/* Breakdown Table */}
            <PaymentBreakdownTable
              paymentBreakdowns={depositData.paymentBreakdowns || []}
              transferDate={transferDate}
              type={type}
              agxMerchantNo={agxMerchantNo}
            />

            {/* Tax Summary Table (for new format) */}
            <TaxSummaryTable
              data={{
                subTotalNonTax: depositData.subTotalNonTax,
                subTotalInclTax10: depositData.subTotalInclTax10,
                subTotalConsumptionTax: depositData.subTotalConsumptionTax,
                subTotalTaxIncl: depositData.subTotalTaxIncl
              }}
              transferDate={transferDate}
              switchLayoutDate={switchLayoutDate}
            />

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="w-full p-4">
                <h3 className="text-xl font-semibold text-center mb-2 text-[#6F6F6E]">
                  売上件数
                </h3>
                <div className="sm:h-96 lg:h-[25rem] h-[25rem]">
                  <Doughnut
                    data={createChartData(chartData.labelsNumberOfSales, chartData.percentageNumberOfSales)}
                    options={CHART_OPTIONS}
                    plugins={createPlugins(depositData.merchantPayments?.[0]?.agxNumberOfSales || 0)}
                  />
                </div>
              </div>

              <div className="w-full p-4">
                <h3 className="text-xl font-semibold text-center mb-2 text-[#6F6F6E]">
                  売上金額
                </h3>
                <div className="sm:h-96 lg:h-[25rem] h-[25rem]">
                  <Doughnut
                    data={createChartData(chartData.labelsSalesAmount, chartData.percentageSalesAmount)}
                    options={CHART_OPTIONS}
                    plugins={createPlugins(depositData.merchantPayments?.[0]?.agxSalesAmount || 0)}
                  />
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
